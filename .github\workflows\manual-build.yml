name: Manual Build

on:
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build for'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - windows
        - macos-intel
        - macos-apple-silicon
        - linux

jobs:
  build-windows:
    if: ${{ github.event.inputs.platform == 'all' || github.event.inputs.platform == 'windows' }}
    runs-on: windows-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Install frontend dependencies
        run: npm ci

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          args: --verbose

      - name: Upload Windows artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-build
          path: |
            src-tauri/target/release/bundle/msi/*.msi
            src-tauri/target/release/*.exe

  build-macos-intel:
    if: ${{ github.event.inputs.platform == 'all' || github.event.inputs.platform == 'macos-intel' }}
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: x86_64-apple-darwin

      - name: Install Rust target
        run: rustup target add x86_64-apple-darwin

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Install frontend dependencies
        run: npm ci

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          args: --verbose --target x86_64-apple-darwin

      - name: Upload macOS Intel artifacts
        uses: actions/upload-artifact@v4
        with:
          name: macos-intel-build
          path: |
            src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/*.dmg
            src-tauri/target/x86_64-apple-darwin/release/bundle/macos/*.app

  build-macos-apple-silicon:
    if: ${{ github.event.inputs.platform == 'all' || github.event.inputs.platform == 'macos-apple-silicon' }}
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: aarch64-apple-darwin

      - name: Install Rust target
        run: rustup target add aarch64-apple-darwin

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Install frontend dependencies
        run: npm ci

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          args: --verbose --target aarch64-apple-darwin

      - name: Upload macOS Apple Silicon artifacts
        uses: actions/upload-artifact@v4
        with:
          name: macos-apple-silicon-build
          path: |
            src-tauri/target/aarch64-apple-darwin/release/bundle/dmg/*.dmg
            src-tauri/target/aarch64-apple-darwin/release/bundle/macos/*.app

  build-linux:
    if: ${{ github.event.inputs.platform == 'all' || github.event.inputs.platform == 'linux' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Linux dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libgtk-3-dev \
            libwebkit2gtk-4.1-dev \
            libappindicator3-dev \
            librsvg2-dev \
            patchelf \
            libssl-dev \
            pkg-config \
            build-essential \
            curl \
            wget \
            file

      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: x86_64-unknown-linux-gnu

      - name: Install Rust target
        run: rustup target add x86_64-unknown-linux-gnu

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Install frontend dependencies
        run: npm ci

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          args: --verbose --target x86_64-unknown-linux-gnu

      - name: Upload Linux artifacts
        uses: actions/upload-artifact@v4
        with:
          name: linux-build
          path: |
            src-tauri/target/x86_64-unknown-linux-gnu/release/bundle/deb/*.deb
            src-tauri/target/x86_64-unknown-linux-gnu/release/bundle/appimage/*.AppImage
            src-tauri/target/x86_64-unknown-linux-gnu/release/atm


