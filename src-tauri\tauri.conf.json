{"productName": "ATM", "version": "0.5.0", "identifier": "com.cubezhao.atm", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"title": "ATM", "width": 850, "height": 700, "resizable": true, "fullscreen": false, "minWidth": 850, "minHeight": 700}], "security": {"csp": "default-src 'self'; connect-src ipc: http://ipc.localhost https://oauth.example.com"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico", "icons/icon.png"], "linux": {"deb": {"depends": ["xdg-utils", "libwebkit2gtk-4.0-0 | libwebkit2gtk-4.1-0"], "files": {"/usr/share/applications/atm.desktop": "./assets/atm.desktop"}}, "appimage": {"bundleMediaFramework": true}}}}