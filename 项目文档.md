# Augment Token Manager 项目文档

## 项目概述

**Augment Token Manager (ATM)** 是一个基于 Tauri 构建的跨平台桌面应用程序，专门用于生成和管理 Augment Code 访问令牌。该应用提供了完整的 OAuth 授权流程、令牌管理、书签管理、邮箱管理等功能，支持本地存储和数据库存储的双重存储方案。

### 技术栈
- **前端**: Vue.js 3 + Vite
- **后端**: Rust + Tauri 2.0
- **数据库**: PostgreSQL (可选)
- **构建工具**: Tauri CLI, Docker
- **UI框架**: 原生CSS + SVG图标

## 项目结构详解

### 根目录文件

| 文件名 | 作用 |
|--------|------|
| `README.md` | 项目说明文档，包含安装指南、使用方法和构建说明 |
| `package.json` | 前端依赖管理，定义了Vue.js和Vite相关依赖 |
| `vite.config.js` | Vite构建配置，设置了Tauri开发环境的端口和路径别名 |
| `index.html` | 应用入口HTML文件 |
| `LICENSE` | MIT开源许可证 |
| `1.png`, `2.png` | 应用截图，用于README展示 |

### 构建和部署文件

| 文件名 | 作用 |
|--------|------|
| `build.ps1` | Windows PowerShell构建脚本，自动检查依赖并构建应用 |
| `build.sh` | macOS/Linux Bash构建脚本，支持跨平台构建 |
| `docker-compose.yml` | Docker编排配置，支持Linux构建、跨平台构建和开发环境 |
| `Dockerfile.build` | 多阶段Docker构建文件，用于生产环境构建 |
| `Dockerfile.cross` | 跨平台构建Docker文件 |
| `Dockerfile.dev` | 开发环境Docker文件 |

### 前端源码结构 (`src/`)

#### 核心文件
| 文件名 | 作用 |
|--------|------|
| `main.js` | Vue应用入口文件，挂载App组件 |
| `App.vue` | 主应用组件，包含完整的UI布局和业务逻辑 |
| `style.css` | 全局样式文件，定义基础样式和响应式布局 |

#### 组件目录 (`src/components/`)
| 组件名 | 作用 |
|--------|------|
| `TokenGenerator.vue` | 令牌生成器组件，处理OAuth授权流程 |
| `TokenList.vue` | 令牌列表组件，显示和管理已保存的令牌 |
| `TokenCard.vue` | 令牌卡片组件，单个令牌的展示和操作 |
| `TokenForm.vue` | 令牌表单组件，用于添加和编辑令牌 |
| `BookmarkManager.vue` | 书签管理组件，管理网页书签 |
| `OutlookManager.vue` | Outlook邮箱管理组件，管理邮箱账户 |
| `EmailViewer.vue` | 邮件查看器组件，显示邮件列表 |
| `EmailDetails.vue` | 邮件详情组件，显示邮件内容 |
| `DatabaseConfig.vue` | 数据库配置组件，配置PostgreSQL连接 |
| `SyncStatus.vue` | 同步状态组件，显示数据同步状态 |

### 后端源码结构 (`src-tauri/`)

#### 配置文件
| 文件名 | 作用 |
|--------|------|
| `Cargo.toml` | Rust项目配置，定义依赖包和项目元信息 |
| `tauri.conf.json` | Tauri应用配置，定义窗口属性、安全策略和打包选项 |
| `build.rs` | Rust构建脚本 |

#### 核心源码 (`src-tauri/src/`)
| 文件名 | 作用 |
|--------|------|
| `main.rs` | 应用主入口，定义所有Tauri命令和应用状态管理 |
| `augment_oauth.rs` | Augment OAuth认证模块，处理PKCE流程和令牌获取 |
| `bookmarks.rs` | 书签管理模块，提供书签的CRUD操作 |
| `outlook_manager.rs` | Outlook邮箱管理模块，处理邮箱认证和邮件操作 |
| `http_server.rs` | HTTP服务器模块，用于OAuth回调处理 |

#### 数据库模块 (`src-tauri/src/database/`)
| 文件名 | 作用 |
|--------|------|
| `mod.rs` | 数据库模块导出文件 |
| `config.rs` | 数据库配置管理，处理PostgreSQL连接配置 |
| `connection.rs` | 数据库连接管理，维护连接池 |
| `migrations.rs` | 数据库迁移脚本，创建和管理表结构 |

#### 存储模块 (`src-tauri/src/storage/`)
| 文件名 | 作用 |
|--------|------|
| `mod.rs` | 存储模块导出文件 |
| `traits.rs` | 存储接口定义，定义统一的存储操作接口 |
| `local_storage.rs` | 本地文件存储实现，使用JSON文件存储数据 |
| `postgres_storage.rs` | PostgreSQL存储实现，使用数据库存储数据 |
| `dual_storage.rs` | 双重存储管理器，同时支持本地和数据库存储 |

### 资源文件

#### 图标资源 (`src-tauri/icons/`, `public/icons/`)
- 包含各种尺寸的应用图标文件
- 支持Windows (.ico)、macOS (.icns)、Linux (.png) 格式

#### Docker构建脚本 (`docker/`)
| 文件名 | 作用 |
|--------|------|
| `build.sh` | Docker构建脚本，支持不同构建模式 |
| `build-all.sh` | 批量构建脚本，构建所有平台版本 |

## 核心功能模块

### 1. OAuth认证模块
- **文件**: `src-tauri/src/augment_oauth.rs`
- **功能**: 
  - 实现PKCE (Proof Key for Code Exchange) 安全流程
  - 生成授权URL和处理回调
  - 获取和刷新访问令牌
  - 检查账户状态和封禁情况

### 2. 令牌管理模块
- **前端**: `src/App.vue`, `src/components/Token*.vue`
- **后端**: `src-tauri/src/main.rs` (令牌相关命令)
- **功能**:
  - 令牌的增删改查操作
  - 令牌有效性验证
  - 批量令牌管理
  - 令牌导入导出

### 3. 存储管理模块
- **文件**: `src-tauri/src/storage/`
- **功能**:
  - 双重存储架构 (本地文件 + PostgreSQL)
  - 数据同步机制
  - 存储状态监控
  - 数据备份和恢复

### 4. 书签管理模块
- **前端**: `src/components/BookmarkManager.vue`
- **后端**: `src-tauri/src/bookmarks.rs`
- **功能**:
  - 网页书签的分类管理
  - 书签导入导出
  - 书签搜索和过滤

### 5. 邮箱管理模块
- **前端**: `src/components/OutlookManager.vue`, `src/components/Email*.vue`
- **后端**: `src-tauri/src/outlook_manager.rs`
- **功能**:
  - Outlook邮箱账户管理
  - 邮件列表查看
  - 邮件详情显示
  - 邮箱状态监控

### 6. 数据库管理模块
- **前端**: `src/components/DatabaseConfig.vue`
- **后端**: `src-tauri/src/database/`
- **功能**:
  - PostgreSQL连接配置
  - 数据库表结构管理
  - 连接状态监控
  - 数据迁移

## 构建和部署

### 本地开发
```bash
# 安装依赖
npm install

# 开发模式
cargo tauri dev
```

### 生产构建
```bash
# Windows
.\build.ps1

# macOS/Linux
./build.sh

# Docker构建
docker-compose --profile build up
```

### 跨平台构建
```bash
# Docker跨平台构建
docker-compose --profile cross up
```

## 配置文件说明

### Tauri配置 (`src-tauri/tauri.conf.json`)
- 应用标识符: `com.cubezhao.atm`
- 窗口尺寸: 850x700 (最小尺寸)
- 安全策略: CSP配置
- 打包选项: 支持所有平台

### 前端配置 (`vite.config.js`)
- 开发服务器端口: 1420
- 路径别名: `@` 指向 `src` 目录
- Tauri集成配置

### Rust配置 (`src-tauri/Cargo.toml`)
- 主要依赖: Tauri 2.0, Serde, Reqwest, Tokio
- 数据库依赖: tokio-postgres, deadpool-postgres
- 加密依赖: aes-gcm, sha2, base64

## 数据存储

### 本地存储
- **位置**: 应用数据目录 (`%APPDATA%` / `~/.config`)
- **格式**: JSON文件
- **文件**: `tokens.json`, `bookmarks.json`, `outlook_credentials.json`

### 数据库存储
- **类型**: PostgreSQL
- **表结构**: tokens, bookmarks, outlook_accounts
- **连接**: 连接池管理
- **同步**: 双向同步机制

## 安全特性

### OAuth安全
- PKCE流程防止授权码拦截
- 状态参数防止CSRF攻击
- 安全的令牌存储

### 数据加密
- 敏感数据AES-GCM加密
- 安全的密钥管理
- TLS数据库连接

### 应用安全
- CSP内容安全策略
- 最小权限原则
- 安全的IPC通信

## 开发指南

### 添加新功能
1. 在 `src-tauri/src/main.rs` 中添加Tauri命令
2. 在对应的Rust模块中实现业务逻辑
3. 在Vue组件中调用Tauri命令
4. 更新UI和样式

### 数据库操作
1. 在 `src-tauri/src/database/migrations.rs` 中添加表结构
2. 在存储模块中实现CRUD操作
3. 在双重存储中添加同步逻辑

### 测试
- 单元测试: `cargo test`
- 集成测试: 手动测试各功能模块
- 跨平台测试: 使用Docker构建测试

这个项目展现了现代桌面应用开发的最佳实践，结合了Web技术的灵活性和原生应用的性能，提供了完整的企业级令牌管理解决方案。
